package com.concise.unify.service.impl;

import com.concise.modular.zwdd.uservo.EmployeeInfo;
import com.concise.unify.service.UnifyUpdateService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * UnifyUpdateService 测试类
 * 
 * <AUTHOR>
 * @date 2024/01/01
 */
@SpringBootTest
@ActiveProfiles("test")
public class UnifyUpdateServiceImplTest {

    @Resource
    private UnifyUpdateService unifyUpdateService;

    /**
     * 测试数据同步清理方法
     * 
     * 注意：这是一个示例测试，实际使用时请根据具体环境调整测试数据
     */
    @Test
    public void testCleanupOrphanedUsers() {
        // 创建测试用的员工信息列表
        List<EmployeeInfo> employeeInfos = createTestEmployeeInfos();
        
        // 执行清理方法
        // 注意：在测试环境中运行此方法，请确保有适当的测试数据
        // 建议在开发环境或测试环境中谨慎运行，避免误删生产数据
        
        System.out.println("开始测试数据同步清理方法...");
        System.out.println("测试员工信息数量: " + employeeInfos.size());
        
        // 取消注释下面的行来实际执行测试
        // unifyUpdateService.cleanupOrphanedUsers(employeeInfos);
        
        System.out.println("测试完成");
    }

    /**
     * 创建测试用的员工信息
     * 
     * @return 员工信息列表
     */
    private List<EmployeeInfo> createTestEmployeeInfos() {
        List<EmployeeInfo> employeeInfos = new ArrayList<>();
        
        // 创建测试员工1
        EmployeeInfo employee1 = new EmployeeInfo();
        employee1.setEmployeeCode("TEST001");
        employee1.setEmployeeName("测试员工1");
        employee1.setStatus("A"); // A表示在职
        employeeInfos.add(employee1);
        
        // 创建测试员工2
        EmployeeInfo employee2 = new EmployeeInfo();
        employee2.setEmployeeCode("TEST002");
        employee2.setEmployeeName("测试员工2");
        employee2.setStatus("A");
        employeeInfos.add(employee2);
        
        return employeeInfos;
    }

    /**
     * 测试空员工列表的情况
     */
    @Test
    public void testCleanupOrphanedUsersWithEmptyList() {
        List<EmployeeInfo> emptyList = new ArrayList<>();
        
        // 这应该直接返回，不执行任何清理操作
        unifyUpdateService.cleanupOrphanedUsers(emptyList);
        
        System.out.println("空列表测试完成");
    }

    /**
     * 测试null员工列表的情况
     */
    @Test
    public void testCleanupOrphanedUsersWithNullList() {
        // 这应该直接返回，不执行任何清理操作
        unifyUpdateService.cleanupOrphanedUsers(null);
        
        System.out.println("null列表测试完成");
    }
}
