package com.concise.unify.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMoziEmployeeListEmployeePositionsByEmployeeCodeResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMoziFusionPageSearchEmployeeResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMoziOrganizationGetOrganizationEmployeeCountResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMoziOrganizationPageOrganizationEmployeePositionsResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMoziOrganizationPageSubOrganizationCodesResponse;
import com.alibaba.xxpt.gateway.shared.api.response.OapiMoziStripLineAddressOrgPageSubOrgsResponse;
import com.alibaba.xxpt.gateway.shared.client.http.api.OapiSpResultContent;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.concise.gen.unifyuser.entity.UnifyUser;
import com.concise.gen.unifyuser.service.UnifyUserService;
import com.concise.gen.unifyusernewextendinfo.entity.UnifyUserNewExtendInfo;
import com.concise.gen.unifyusernewextendinfo.service.UnifyUserNewExtendInfoService;
import com.concise.modular.zwdd.ZwddUtil;
import com.concise.modular.zwdd.orgvo.OrgLine;
import com.concise.modular.zwdd.orgvo.Organization;
import com.concise.modular.zwdd.uservo.EmployeeInfo;
import com.concise.modular.zwdd.uservo.EmployeePosition;
import com.concise.unify.depart.LxDepartVo;
import com.concise.unify.enums.OrganizationTypeEnum;
import com.concise.unify.service.UnifyPushService;
import com.concise.unify.service.UnifyUpdateService;
import com.concise.unify.unifydeparthalf.entity.UnifyDepartHalf;
import com.concise.unify.unifydeparthalf.service.UnifyDepartHalfService;
import com.concise.unify.unifydepartnew.entity.UnifyDepartNew;
import com.concise.unify.unifydepartnew.service.UnifyDepartNewService;
import com.concise.unify.unifyusernew.entity.UnifyUserNew;
import com.concise.unify.unifyusernew.service.UnifyUserNewService;
import com.concise.unify.user.LxExtendInfosVo;
import com.concise.unify.user.LxUserVo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/8/7
 */
@Slf4j
@Service
public class UnifyUpdateServiceImpl implements UnifyUpdateService {
    @Resource
    private UnifyDepartNewService unifyDepartNewService;

    @Resource
    private UnifyPushService unifyPushService;

    @Resource
    private UnifyUserNewService unifyUserNewService;

    @Resource
    private UnifyUserService unifyUserService;

    @Resource
    private UnifyDepartHalfService unifyDepartHalfService;

    @Resource
    private UnifyUserNewExtendInfoService unifyUserNewExtendInfoService;

    @Override
    public void updateAllOrgData() {
        List<OrgLine> allOrgLine = new ArrayList<>();
        List<Organization> allOrganization = new ArrayList<>();
//        List<UnifyDepartHalf> departHalfList = unifyDepartHalfService.list();
        //先获取省司法厅和各地市
        List<OrgLine> topOrgLine = getAllOrgLine("LD_d858587bd54945edb6232980ba1549f7", "LD_d858587bd54945edb6232980ba1549f7");
        log.info(JSONObject.toJSONString(topOrgLine));
        if (CollectionUtil.isNotEmpty(topOrgLine)) {
            dealFirstLine(topOrgLine);
            for (OrgLine orgLine : topOrgLine) {
                List<OrgLine> cityOrgLine = getAllOrgLine(orgLine.getDomainCode(), orgLine.getOrganizationCode());
                dealSecondLine(cityOrgLine);
                //省级
                if ("省司法厅".equals(orgLine.getOrganizationName())) {
                    List<Organization> organizationList = getAllOrganizationProvince(orgLine.getRefOrganizationCode());
                    allOrganization.addAll(organizationList);
                }
                //地市
                if (CollectionUtil.isNotEmpty(cityOrgLine)) {
                    for (OrgLine line : cityOrgLine) {
                        if (ObjectUtil.isNotEmpty(line.getRefOrganizationCode())) {
                            List<Organization> organizationList = getAllOrganization(line.getRefOrganizationCode());
                            if (CollectionUtil.isNotEmpty(organizationList)) {
                                allOrganization.addAll(organizationList);

                            }

                        }
                    }
                }
            }
        }

        dealOrganization(allOrganization);
        log.debug(JSONObject.toJSONString(allOrgLine));
        log.debug(JSONObject.toJSONString(allOrganization));

    }

    private List<Organization> getAllOrganizationProvince(String organizationCode) {
        List<String> orgIds = new ArrayList<>();
        List<Organization> organizationList = new ArrayList<>();
        OapiMoziOrganizationPageSubOrganizationCodesResponse response = ZwddUtil.pageSubOrganizationCodes(1, organizationCode);
        if (response.getContent() != null) {
            Long totalSize = response.getContent().getTotalSize();
            String data = response.getContent().getData();
            List<String> stringList = JSONObject.parseArray(data, String.class);
            if (CollectionUtil.isNotEmpty(stringList)) {
                orgIds.addAll(stringList);
                if (totalSize > 100) {
                    int t = (int) (totalSize / 2);
                    for (int l = 0; l < t + 1; l++) {
                        OapiMoziOrganizationPageSubOrganizationCodesResponse responsePage = ZwddUtil.pageSubOrganizationCodes(l + 2, organizationCode);
                        if (responsePage.getContent() != null && responsePage.getContent().getData() != null) {
                            String dataPage = responsePage.getContent().getData();
                            List<String> lineListPage = JSONObject.parseArray(dataPage, String.class);
                            orgIds.addAll(lineListPage);
                        }
                    }
                }
            }

        }
        if (!orgIds.isEmpty()) {

            List<Organization> organizationsByCodes = ZwddUtil.listOrganizationsByCodes(orgIds);
            if (!organizationsByCodes.isEmpty()) {
                for (Organization organizationsByCode : organizationsByCodes) {
                    //此处排除了省司法厅下属单位及下面的机构，这里2万人，数据太多，暂不同步
                    if (!organizationsByCode.isLeaf() && !"GO_9326af6fd14e41d6b0d6b86d6c4f52d5".equals(organizationsByCode.getOrganizationCode())) {
                        organizationList.add(organizationsByCode);
                        List<Organization> childList = getAllOrganization(organizationsByCode.getOrganizationCode());
                        if (CollectionUtil.isNotEmpty(childList)) {
                            organizationList.addAll(childList);
                        }
                    } else {
                        organizationList.add(organizationsByCode);
                    }
                }
            }

        }
        return organizationList;
    }

    @Override
    public void updateAllDataForUser() {

        List<UnifyDepartHalf> departHalfList = unifyDepartHalfService.list();
        //先获取省司法厅和各地市
        List<OrgLine> topOrgLine = getAllOrgLine("LD_d858587bd54945edb6232980ba1549f7", "LD_d858587bd54945edb6232980ba1549f7");
        log.info(JSONObject.toJSONString(topOrgLine));
        if (CollectionUtil.isNotEmpty(topOrgLine)) {
            dealFirstLine(topOrgLine);
            for (OrgLine orgLine : topOrgLine) {
                List<OrgLine> cityOrgLine = getAllOrgLine(orgLine.getDomainCode(), orgLine.getOrganizationCode());
                dealSecondLine(cityOrgLine);
                //省级
                if ("省司法厅".equals(orgLine.getOrganizationName())) {
                    List<Organization> organizationList = getAllOrganizationProvince(orgLine.getRefOrganizationCode());
                    for (Organization organization : organizationList) {
                        if (!"下属单位".equals(organization.getOrganizationName())) {
                            updateAllUser(organization.getOrganizationCode(), departHalfList);
                        }
                    }
                }
                //地市
                if (CollectionUtil.isNotEmpty(cityOrgLine)) {
                    for (OrgLine line : cityOrgLine) {
                        if (ObjectUtil.isNotEmpty(line.getRefOrganizationCode())) {
                            //穿插人员查询
                            updateAllUser(line.getRefOrganizationCode(), departHalfList);

                        }
                    }
                }
            }
        }
    }

    private void dealFirstLine(List<OrgLine> allOrgLine) {
        for (int i = 0; i < allOrgLine.size(); i++) {
            OrgLine orgLine = allOrgLine.get(i);
            LxDepartVo lxDepartVo = new LxDepartVo();
            lxDepartVo.setSort(i + 1);
            if ("省司法厅".equals(orgLine.getOrganizationName())) {
                lxDepartVo.setId(orgLine.getRefOrganizationCode());
            } else {
                lxDepartVo.setId(orgLine.getOrganizationCode());
            }
            lxDepartVo.setParentId(orgLine.getParentCode());
            lxDepartVo.setName(orgLine.getOrganizationName());
            lxDepartVo.setType(OrganizationTypeEnum.getEnumByCode(orgLine.getType()).getType());
            if (!"A".equals(orgLine.getStatus())) {
                lxDepartVo.setOperatorType(2);
            }
            UnifyDepartNew departNew = unifyDepartNewService.getById(orgLine.getOrganizationCode());
            if (ObjectUtil.isNotEmpty(departNew)) {
                lxDepartVo.setOperatorType(1);
                LxDepartVo departVo = JSONObject.parseObject(departNew.getJsonInfo(), LxDepartVo.class);
                if (!lxDepartVo.equals(departVo) && ObjectUtil.isNotEmpty(lxDepartVo.getId())) {
                    unifyPushService.handleDepart(lxDepartVo);
                }
            } else {
                lxDepartVo.setOperatorType(0);
                if (ObjectUtil.isNotEmpty(lxDepartVo.getId())) {
                    unifyPushService.handleDepart(lxDepartVo);
                }
            }
        }
    }

    private void dealSecondLine(List<OrgLine> allOrgLine) {
        for (int i = 0; i < allOrgLine.size(); i++) {
            OrgLine orgLine = allOrgLine.get(i);
            LxDepartVo lxDepartVo = new LxDepartVo();
            lxDepartVo.setSort(i + 1);
            lxDepartVo.setId(orgLine.getRefOrganizationCode());
            lxDepartVo.setParentId(orgLine.getParentCode());
            lxDepartVo.setName(orgLine.getOrganizationName());
            lxDepartVo.setType(OrganizationTypeEnum.getEnumByCode(orgLine.getType()).getType());
            if (!"A".equals(orgLine.getStatus())) {
                lxDepartVo.setOperatorType(2);
            }
            UnifyDepartNew departNew = unifyDepartNewService.getById(orgLine.getOrganizationCode());
            if (ObjectUtil.isNotEmpty(departNew)) {
                lxDepartVo.setOperatorType(1);
                LxDepartVo departVo = JSONObject.parseObject(departNew.getJsonInfo(), LxDepartVo.class);
                if (!lxDepartVo.equals(departVo) && ObjectUtil.isNotEmpty(lxDepartVo.getId())) {
                    unifyPushService.handleDepart(lxDepartVo);
                }
            } else {
                lxDepartVo.setOperatorType(0);
                if (ObjectUtil.isNotEmpty(lxDepartVo.getId())) {
                    unifyPushService.handleDepart(lxDepartVo);
                }
            }
        }
    }

    /**
     * 获取条线机构
     *
     * @param domainCode
     * @param organizationCode
     * @return
     */
    public List<OrgLine> getAllOrgLine(String domainCode, String organizationCode) {
        List<OrgLine> orgLineList = new ArrayList<>();
        OapiMoziStripLineAddressOrgPageSubOrgsResponse response = ZwddUtil.pageSubOrgs(1, domainCode, organizationCode);
        if (ObjectUtil.isNotEmpty(response.getContent()) && ObjectUtil.isNotEmpty(response.getContent().getData())) {
            String data = response.getContent().getData();
            List<OrgLine> lineList = JSONObject.parseArray(data, OrgLine.class);
            orgLineList.addAll(lineList);
            Long totalSize = response.getContent().getTotalSize();
            if (totalSize > 100) {
                long t = totalSize / 2;
                for (long l = 0; l < t + 1; l++) {
                    OapiMoziStripLineAddressOrgPageSubOrgsResponse responsePage = ZwddUtil.pageSubOrgs((int) (l + 2), domainCode, organizationCode);
                    if (ObjectUtil.isNotEmpty(responsePage.getContent()) && ObjectUtil.isNotEmpty(responsePage.getContent().getData())) {
                        String dataPage = responsePage.getContent().getData();
                        List<OrgLine> lineListPage = JSONObject.parseArray(dataPage, OrgLine.class);
                        orgLineList.addAll(lineListPage);
                    }
                }
            }
        }
        return orgLineList;
    }

    /**
     * 根据code获取所有下级组织
     *
     * @param organizationCode
     * @return
     */
    public List<Organization> getAllOrganization(String organizationCode) {
        List<String> orgIds = new ArrayList<>();
        List<Organization> organizationList = new ArrayList<>();
        OapiMoziOrganizationPageSubOrganizationCodesResponse response = ZwddUtil.pageSubOrganizationCodes(1, organizationCode);
        if (response.getContent() != null) {
            Long totalSize = response.getContent().getTotalSize();
            String data = response.getContent().getData();
            List<String> stringList = JSONObject.parseArray(data, String.class);
            if (CollectionUtil.isNotEmpty(stringList)) {
                orgIds.addAll(stringList);
                if (totalSize > 100) {
                    int t = (int) (totalSize / 2);
                    for (int l = 0; l < t + 1; l++) {
                        OapiMoziOrganizationPageSubOrganizationCodesResponse responsePage = ZwddUtil.pageSubOrganizationCodes(l + 2, organizationCode);
                        if (responsePage.getContent() != null && responsePage.getContent().getData() != null) {
                            String dataPage = responsePage.getContent().getData();
                            List<String> lineListPage = JSONObject.parseArray(dataPage, String.class);
                            orgIds.addAll(lineListPage);
                        }
                    }
                }
            }

        }
        if (!orgIds.isEmpty()) {
            int size = orgIds.size();
            if (size > 100) {
                int j = (size / 100) + 1;
                for (int i = 0; i < j; i++) {
                    List<Organization> organizationsByCodes = ZwddUtil.listOrganizationsByCodes(orgIds.subList(100 * i, Math.min(100 * (i + 1), size)));
                    if (!organizationsByCodes.isEmpty()) {
                        organizationList.addAll(organizationsByCodes);
                        for (Organization organizationsByCode : organizationsByCodes) {
                            if (!organizationsByCode.isLeaf()) {
                                List<Organization> childList = getAllOrganization(organizationsByCode.getOrganizationCode());
                                if (CollectionUtil.isNotEmpty(childList)) {
                                    organizationList.addAll(childList);
                                }
                            }
                        }
                    }
                }
            } else {
                List<Organization> organizationsByCodes = ZwddUtil.listOrganizationsByCodes(orgIds);
                if (!organizationsByCodes.isEmpty()) {
                    for (Organization organizationsByCode : organizationsByCodes) {
                        //此处排除了省司法厅下属单位及下面的机构，这里2万人，数据太多，暂不同步
                        if (!organizationsByCode.isLeaf()) {
                            organizationList.add(organizationsByCode);
                            List<Organization> childList = getAllOrganization(organizationsByCode.getOrganizationCode());
                            if (CollectionUtil.isNotEmpty(childList)) {
                                organizationList.addAll(childList);
                            }
                        }else {
                            organizationList.add(organizationsByCode);
                        }
                    }
                }
            }
        }
        return organizationList;
    }

    public List<Organization> getAllOrganizationByList(List<String> orgIds) {
        List<Organization> organizationList = new ArrayList<>();
        int size = orgIds.size();
        if (size > 100) {
            int j = (size / 100) + 1;
            for (int i = 0; i < j; i++) {
                List<Organization> organizationsByCodes = ZwddUtil.listOrganizationsByCodes(orgIds.subList(100 * i, Math.min(100 * (i + 1), size)));
                if (!organizationsByCodes.isEmpty()) {
                    organizationList.addAll(organizationsByCodes);
                    for (Organization organizationsByCode : organizationsByCodes) {
                        if (!organizationsByCode.isLeaf()) {
                            List<Organization> childList = getAllOrganization(organizationsByCode.getOrganizationCode());
                            if (CollectionUtil.isNotEmpty(childList)) {
                                organizationList.addAll(childList);
                            }
                        }
                    }
                }
            }
        } else {
            List<Organization> organizationsByCodes = ZwddUtil.listOrganizationsByCodes(orgIds);
            if (!organizationsByCodes.isEmpty()) {
                organizationList.addAll(organizationsByCodes);
                for (Organization organizationsByCode : organizationsByCodes) {
                    if (!organizationsByCode.isLeaf()) {
                        List<Organization> childList = getAllOrganization(organizationsByCode.getOrganizationCode());
                        if (CollectionUtil.isNotEmpty(childList)) {
                            organizationList.addAll(childList);
                        }
                    }
                }
            }
        }
        return organizationList;
    }

    public void dealOrganization(List<Organization> organizationList) {
        for (Organization organization : organizationList) {
            LxDepartVo lxDepartVo = new LxDepartVo();
            lxDepartVo.setId(organization.getOrganizationCode());
            lxDepartVo.setSort(organization.getDisplayOrder());
            lxDepartVo.setParentId(organization.getParentCode());
            lxDepartVo.setName(organization.getOrganizationName());
            lxDepartVo.setType(OrganizationTypeEnum.getEnumByCode(organization.getTypeCode()).getType());
            lxDepartVo.setLeaf(organization.isLeaf());
            if ("F".equals(organization.getStatus())) {
                lxDepartVo.setOperatorType(2);
            }
            UnifyDepartNew departNew = unifyDepartNewService.getById(organization.getOrganizationCode());
            if (ObjectUtil.isNotEmpty(departNew)) {
                lxDepartVo.setOperatorType(1);
                LxDepartVo departVo = JSONObject.parseObject(departNew.getJsonInfo(), LxDepartVo.class);
                if (!lxDepartVo.equals(departVo)) {
                    unifyPushService.handleDepart(lxDepartVo);
                    unifyPushService.handleDepartSync(lxDepartVo, System.currentTimeMillis());
                }
            } else {
                lxDepartVo.setOperatorType(0);
                unifyPushService.handleDepart(lxDepartVo);
                unifyPushService.handleDepartSync(lxDepartVo, System.currentTimeMillis());
            }
        }

    }

    @Async
    @Override
    public void updateAllUser(String organizationCode, List<UnifyDepartHalf> unifyDepartHalfList) {
        // 用于收集所有员工信息，以便后续进行数据清理
        List<EmployeeInfo> allEmployeeInfos = new ArrayList<>();

        OapiMoziOrganizationGetOrganizationEmployeeCountResponse countResponse = ZwddUtil.getOrganizationEmployeeCount(organizationCode);
        OapiSpResultContent content = countResponse.getContent();
        if (content != null) {
            String data = content.getData();
            if (data != null) {
                int personCount = Integer.parseInt(data);
                //如果人数小于1万，直接调用接口获取下面的所有人
                if (personCount < 10000) {
                    int pageTotal = personCount / 100 + 1;
                    for (int i = 0; i < pageTotal; i++) {
                        OapiMoziOrganizationPageOrganizationEmployeePositionsResponse response = ZwddUtil.pageOrganizationEmployeePositions(i + 1, organizationCode);
                        if (response.getContent() != null) {
                            String userData = response.getContent().getData();
                            List<EmployeeInfo> employeeInfos = JSONObject.parseArray(userData, EmployeeInfo.class);
                            if (CollectionUtil.isNotEmpty(employeeInfos)) {
                                // 收集员工信息用于后续清理
                                allEmployeeInfos.addAll(employeeInfos);

                                for (EmployeeInfo employeeInfo : employeeInfos) {
                                    UnifyUserNew unifyUserNew = unifyUserNewService.getById(employeeInfo.getEmployeeCode());
                                    LxUserVo lxUserVo = userInfoBuild(organizationCode, employeeInfo, unifyUserNew, unifyDepartHalfList);
                                    if (ObjectUtil.isNotEmpty(unifyUserNew)) {
                                        LxUserVo userVo = JSONObject.parseObject(unifyUserNew.getJsonInfo(), LxUserVo.class);
                                        if (!lxUserVo.equals(userVo)) {
                                            log.debug("前后不一致：" + JSONObject.toJSONString(lxUserVo) + JSONObject.toJSONString(userVo));
                                            unifyPushService.handleUser(lxUserVo);
                                            unifyPushService.handleUserSync(lxUserVo, System.currentTimeMillis());
                                        }
                                    } else {
                                        unifyPushService.handleUser(lxUserVo);
                                        unifyPushService.handleUserSync(lxUserVo, System.currentTimeMillis());
                                    }
                                }
                            }
                            System.out.println(employeeInfos);
                        }
                    }

                }
            }

            log.debug(JSONObject.toJSONString(countResponse.getContent().getData()));
        }

        // 执行数据同步清理
        // 当获取到员工信息且数量 >= 1 时，执行清理逻辑
        if (CollectionUtil.isNotEmpty(allEmployeeInfos)) {
            log.info("开始执行组织 {} 的数据同步清理，员工总数: {}", organizationCode, allEmployeeInfos.size());
            cleanupOrphanedUsers(allEmployeeInfos);
        }
    }

    private LxUserVo userInfoBuild(String organizationCode, EmployeeInfo employeeInfo, UnifyUserNew unifyUserNew, List<UnifyDepartHalf> unifyDepartHalfList) {
        LxUserVo lxUserVo = new LxUserVo();
        lxUserVo.setId(employeeInfo.getEmployeeCode());
        lxUserVo.setSex(Integer.parseInt(employeeInfo.getEmpGender()));
        lxUserVo.setName(employeeInfo.getEmployeeName());
        OapiMoziFusionPageSearchEmployeeResponse response = ZwddUtil.pageSearchEmployee(1, organizationCode, employeeInfo.getEmployeeName());
        OapiMoziEmployeeListEmployeePositionsByEmployeeCodeResponse employeeResponse = ZwddUtil.listEmployeePositionsByEmployeeCode(unifyUserNew.getId());

        if (response.getContent() != null) {
            String data = response.getContent().getData();
            if (data != null) {
                JSONArray jsonArray = JSONObject.parseArray(data);
                for (Object jsonString : jsonArray) {
                    JSONObject jsonObject = JSONObject.parseObject(jsonString.toString());
                    String accountId = jsonObject.getString("accountId");
                    String account = jsonObject.getString("account");
                    lxUserVo.setUserName(account);
                    lxUserVo.setAccountId(accountId);
                    break;
                }

            }
        }
        if (!"A".equals(employeeInfo.getStatus())) {
            lxUserVo.setOperatorType(2);
        } else {
            if (ObjectUtil.isNotEmpty(unifyUserNew)) {
                lxUserVo.setOperatorType(1);
            } else {
                lxUserVo.setOperatorType(0);
            }
        }
        List<EmployeePosition> positionList = new ArrayList<>();
        List<LxExtendInfosVo> lxExtendInfosVoList = new ArrayList<>();
        if (employeeResponse.getContent() != null) {
            String data = employeeResponse.getContent().getData();
            List<EmployeePosition> employeePositions = JSONObject.parseArray(data, EmployeePosition.class);
            if (CollectionUtil.isNotEmpty(employeePositions)) {
                positionList.addAll(employeePositions);
            }
        } else {
            positionList = employeeInfo.getGovEmployeePositions();
        }

        for (EmployeePosition employeePosition : positionList) {
            LxExtendInfosVo lxExtendInfosVo = new LxExtendInfosVo();
            lxExtendInfosVo.setUserId(employeePosition.getEmployeeCode());
            lxExtendInfosVo.setPosition(employeePosition.getGovEmpPosJob());
            lxExtendInfosVo.setRank(employeePosition.getVisibilityIndicatorCode());
            lxExtendInfosVo.setUserDeptId(employeePosition.getOrganizationCode());
            lxExtendInfosVo.setWorkPhone(employeePosition.getGovEmpPosPhoneNo());
            lxExtendInfosVo.setWorkAddress(employeePosition.getGovEmpPosAddress());
            lxExtendInfosVo.setSorts(employeePosition.getOrderInOrganization());
            UnifyDepartNew departNew = unifyDepartNewService.getById(employeePosition.getOrganizationCode());
            if (ObjectUtil.isNotEmpty(departNew)) {
                lxExtendInfosVo.setUserDeptName(departNew.getName());
            }
            Optional<UnifyDepartHalf> halfOptional = unifyDepartHalfList.stream().filter(e -> e.getOrganizationUuid().equals(lxExtendInfosVo.getUserDeptId())).findFirst();
            if (halfOptional.isPresent()) {
                UnifyDepartHalf unifyDepartHalf = halfOptional.get();
                lxExtendInfosVo.setJzjg(unifyDepartHalf.getOldOrgId());
                lxExtendInfosVo.setJzjgName(unifyDepartHalf.getOldOrgName());
            }
            lxExtendInfosVoList.add(lxExtendInfosVo);
        }
        if (CollectionUtil.isNotEmpty(lxExtendInfosVoList)) {
            List<String> collect = lxExtendInfosVoList.stream().map(LxExtendInfosVo::getUserDeptId).collect(Collectors.toList());
            lxUserVo.setDeptId(String.join(",", collect));
        }
        //补充手机号
        String phone = lxUserVo.getPhone();

        if (ObjectUtil.isNotNull(unifyUserNew)) {
            UnifyUser unifyUser = unifyUserService.getById(unifyUserNew.getId());
            if (ObjectUtil.isNotEmpty(unifyUser)) {
                phone = unifyUser.getPhoneNumbers();
            } else if (ObjectUtil.isNotEmpty(unifyUserNew.getPhone())) {
                phone = unifyUserNew.getPhone();
            }

        }
        lxUserVo.setPhone(phone);
        lxUserVo.setExtendInfos(lxExtendInfosVoList);
        return lxUserVo;
    }

    @Override
    @Cacheable(value = "testCache")
    public String testCache(String number) {
        return "123";
    }

    @Override
    @CacheEvict(value = "testCache")
    public void resetCache(String number) {
    }

    /**
     * 数据同步清理方法
     * 当查询得到 List<EmployeeInfo> employeeInfos 且列表大小 >= 1 时执行清理逻辑
     *
     * 功能说明：
     * 1. 查询 unify_user_new 表及其相关联的表
     * 2. 找出满足以下条件的用户记录：
     *    - 用户不在 employeeInfos 列表中（按工号 employeeCode 比较）
     *    - 但在相关表中该用户仍然关联到某个组织单位
     * 3. 对这些用户执行数据移除操作（软删除）
     * 4. 执行完成后触发组织架构数据推送
     *
     * 使用示例：
     * <pre>
     * // 在获取到员工信息后调用清理方法
     * List<EmployeeInfo> employeeInfos = getEmployeeInfosFromZwdd();
     * if (CollectionUtil.isNotEmpty(employeeInfos)) {
     *     cleanupOrphanedUsers(employeeInfos);
     * }
     * </pre>
     *
     * @param employeeInfos 从浙政钉获取的员工信息列表，用于确定当前有效的员工
     */
    public void cleanupOrphanedUsers(List<EmployeeInfo> employeeInfos) {
        if (CollectionUtil.isEmpty(employeeInfos)) {
            log.info("员工信息列表为空，跳过数据清理");
            return;
        }

        log.info("开始执行数据同步清理，当前员工信息数量: {}", employeeInfos.size());

        try {
            // 1. 构建当前有效员工的工号集合（用于快速查找）
            Set<String> validEmployeeCodes = employeeInfos.stream()
                    .map(EmployeeInfo::getEmployeeCode)
                    .filter(ObjectUtil::isNotEmpty)
                    .collect(Collectors.toSet());

            log.info("有效员工工号数量: {}", validEmployeeCodes.size());

            // 2. 查询 unify_user_new 表中所有用户记录
            List<UnifyUserNew> allUnifyUsers = unifyUserNewService.list();
            log.info("unify_user_new 表中用户总数: {}", allUnifyUsers.size());

            // 3. 找出需要清理的孤立用户
            List<UnifyUserNew> orphanedUsers = findOrphanedUsers(allUnifyUsers, validEmployeeCodes);

            if (CollectionUtil.isEmpty(orphanedUsers)) {
                log.info("未发现需要清理的孤立用户");
                return;
            }

            log.info("发现需要清理的孤立用户数量: {}", orphanedUsers.size());

            // 4. 执行数据移除操作
            removeOrphanedUserData(orphanedUsers);

            // 5. 触发组织架构数据推送
            triggerOrganizationDataPush();

            log.info("数据同步清理完成");

        } catch (Exception e) {
            log.error("数据同步清理过程中发生异常", e);
            throw new RuntimeException("数据同步清理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查找孤立用户
     * 孤立用户定义：在 unify_user_new 表中存在，但不在当前有效员工列表中，且仍然关联到某个组织单位
     *
     * @param allUnifyUsers 所有统一用户记录
     * @param validEmployeeCodes 有效员工工号集合
     * @return 孤立用户列表
     */
    private List<UnifyUserNew> findOrphanedUsers(List<UnifyUserNew> allUnifyUsers, Set<String> validEmployeeCodes) {
        List<UnifyUserNew> orphanedUsers = new ArrayList<>();

        for (UnifyUserNew user : allUnifyUsers) {
            // 跳过已删除的用户
            if (user.getDeleteTime() != null) {
                continue;
            }

            // 检查用户是否在有效员工列表中
            if (validEmployeeCodes.contains(user.getId())) {
                continue;
            }

            // 检查用户是否仍然关联到组织单位
            if (isUserLinkedToOrganization(user)) {
                orphanedUsers.add(user);
                log.debug("发现孤立用户: {} ({}), 关联组织: {}", user.getName(), user.getId(), user.getDeptName());
            }
        }

        return orphanedUsers;
    }

    /**
     * 检查用户是否仍然关联到组织单位
     *
     * @param user 用户记录
     * @return true 如果用户仍然关联到组织单位
     */
    private boolean isUserLinkedToOrganization(UnifyUserNew user) {
        // 检查主要部门关联
        if (ObjectUtil.isNotEmpty(user.getDeptId())) {
            return true;
        }

        // 检查扩展信息中的部门关联
        List<UnifyUserNewExtendInfo> extendInfos = unifyUserNewExtendInfoService.list(
                new QueryWrapper<UnifyUserNewExtendInfo>()
                        .eq("user_id", user.getId())
        );

        if (CollectionUtil.isNotEmpty(extendInfos)) {
            for (UnifyUserNewExtendInfo extendInfo : extendInfos) {
                if (ObjectUtil.isNotEmpty(extendInfo.getUserDeptId()) ||
                    ObjectUtil.isNotEmpty(extendInfo.getJzjg())) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 移除孤立用户数据
     *
     * @param orphanedUsers 孤立用户列表
     */
    private void removeOrphanedUserData(List<UnifyUserNew> orphanedUsers) {
        int removedCount = 0;

        for (UnifyUserNew user : orphanedUsers) {
            try {
                log.info("开始清理孤立用户: {} ({})", user.getName(), user.getId());

                // 1. 清理用户扩展信息
                removeUserExtendInfo(user.getId());

                // 2. 标记用户为删除状态（软删除）
                markUserAsDeleted(user);

                removedCount++;
                log.info("成功清理孤立用户: {} ({})", user.getName(), user.getId());

            } catch (Exception e) {
                log.error("清理孤立用户失败: {} ({})", user.getName(), user.getId(), e);
                // 继续处理其他用户，不中断整个清理过程
            }
        }

        log.info("孤立用户数据清理完成，成功清理 {} 个用户", removedCount);
    }

    /**
     * 清理用户扩展信息
     *
     * @param userId 用户ID
     */
    private void removeUserExtendInfo(String userId) {
        List<UnifyUserNewExtendInfo> extendInfos = unifyUserNewExtendInfoService.list(
                new QueryWrapper<UnifyUserNewExtendInfo>()
                        .eq("user_id", userId)
        );

        if (CollectionUtil.isNotEmpty(extendInfos)) {
            for (UnifyUserNewExtendInfo extendInfo : extendInfos) {
                unifyUserNewExtendInfoService.removeById(extendInfo.getId());
                log.debug("删除用户扩展信息: userId={}, extendInfoId={}", userId, extendInfo.getId());
            }
        }
    }

    /**
     * 标记用户为删除状态
     *
     * @param user 用户记录
     */
    private void markUserAsDeleted(UnifyUserNew user) {
        user.setDeleteTime(new java.util.Date());
        user.setOperatorType("2"); // 2表示删除操作

        // 构建删除状态的用户VO用于推送
        LxUserVo lxUserVo = buildDeletedUserVo(user);

        // 更新数据库记录
        unifyUserNewService.updateById(user);

        // 推送删除状态到其他系统
        unifyPushService.handleUser(lxUserVo);
        unifyPushService.handleUserSync(lxUserVo, System.currentTimeMillis());

        log.debug("标记用户为删除状态: {} ({})", user.getName(), user.getId());
    }

    /**
     * 构建删除状态的用户VO
     *
     * @param user 用户记录
     * @return 删除状态的用户VO
     */
    private LxUserVo buildDeletedUserVo(UnifyUserNew user) {
        LxUserVo lxUserVo = new LxUserVo();
        lxUserVo.setId(user.getId());
        lxUserVo.setName(user.getName());
        lxUserVo.setUserName(user.getUserName());
        lxUserVo.setPhone(user.getPhone());
        lxUserVo.setAccountId(user.getAccountId());
        lxUserVo.setOperatorType(2); // 删除操作
        lxUserVo.setExtendInfos(new ArrayList<>()); // 清空扩展信息

        return lxUserVo;
    }

    /**
     * 触发组织架构数据推送
     * 清理完成后，触发组织架构数据推送以确保各系统数据一致性
     */
    private void triggerOrganizationDataPush() {
        try {
            log.info("开始触发组织架构数据推送");

            // 这里可以根据实际需求选择推送范围
            // 可以推送所有组织架构数据，或者只推送受影响的组织

            // 示例：推送所有部门数据（可根据实际需求调整）
            List<UnifyDepartNew> allDepartments = unifyDepartNewService.list();

            for (UnifyDepartNew department : allDepartments) {
                if (ObjectUtil.isNotEmpty(department.getJsonInfo())) {
                    try {
                        LxDepartVo lxDepartVo = JSONObject.parseObject(department.getJsonInfo(), LxDepartVo.class);
                        unifyPushService.handleDepart(lxDepartVo);
                        unifyPushService.handleDepartSync(lxDepartVo, System.currentTimeMillis());
                    } catch (Exception e) {
                        log.warn("推送部门数据失败: {} ({})", department.getName(), department.getId(), e);
                    }
                }
            }

            log.info("组织架构数据推送完成");

        } catch (Exception e) {
            log.error("触发组织架构数据推送失败", e);
            // 推送失败不应该影响清理操作的成功，所以这里只记录日志
        }
    }
}
