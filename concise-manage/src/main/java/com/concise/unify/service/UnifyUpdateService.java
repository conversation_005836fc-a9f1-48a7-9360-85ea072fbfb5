package com.concise.unify.service;

import java.util.List;

import com.concise.modular.zwdd.uservo.EmployeeInfo;
import com.concise.unify.unifydeparthalf.entity.UnifyDepartHalf;

/**
 * <AUTHOR>
 * @date 2023/8/7
 * 更新统一用户和统一组织机构service
 */
public interface UnifyUpdateService {

    /**
     * 更新全部机构数据
     */
    void updateAllOrgData();

    /**
     * @param organizationCode    更新机构下全部用户
     * @param unifyDepartHalfList
     */
    void updateAllUser(String organizationCode, List<UnifyDepartHalf> unifyDepartHalfList);

    String testCache(String number);

    void resetCache(String number);

    void updateAllDataForUser();

    /**
     * 数据同步清理方法
     * 当查询得到 List<EmployeeInfo> employeeInfos 且列表大小 >= 1 时执行清理逻辑
     *
     * @param employeeInfos 从浙政钉获取的员工信息列表
     */
    void cleanupOrphanedUsers(List<EmployeeInfo> employeeInfos);
}
