# 数据同步清理方法使用说明

## 概述

`cleanupOrphanedUsers` 方法是一个数据同步清理功能，用于清理在 `unify_user_new` 表中存在但不在当前有效员工列表中的孤立用户数据。

## 功能特性

### 1. 主要功能
- **孤立用户识别**：识别不在当前员工列表中但仍关联组织的用户
- **数据清理**：清理用户扩展信息和主表记录
- **软删除**：标记删除而非物理删除，保留数据完整性
- **组织推送**：清理完成后触发组织架构数据推送

### 2. 清理逻辑
1. 构建当前有效员工的工号集合（基于 `EmployeeInfo.employeeCode`）
2. 查询 `unify_user_new` 表中所有用户记录
3. 识别孤立用户：
   - 用户不在有效员工列表中
   - 用户未被标记为删除（`deleteTime` 为空）
   - 用户仍然关联到组织单位
4. 执行清理操作：
   - 删除 `unify_user_new_extend_info` 表中的扩展信息
   - 标记 `unify_user_new` 表中的用户为删除状态
   - 推送删除状态到其他系统

## 使用方法

### 1. 基本调用
```java
@Resource
private UnifyUpdateService unifyUpdateService;

public void performDataCleanup() {
    // 获取当前有效的员工信息列表
    List<EmployeeInfo> employeeInfos = getEmployeeInfosFromZwdd();
    
    // 执行数据清理
    if (CollectionUtil.isNotEmpty(employeeInfos)) {
        unifyUpdateService.cleanupOrphanedUsers(employeeInfos);
    }
}
```

### 2. 集成到现有同步流程
该方法已经集成到 `updateAllUser` 方法中，会在处理完员工信息后自动执行清理：

```java
// 在 updateAllUser 方法中自动调用
public void updateAllUser(String organizationCode, List<UnifyDepartHalf> unifyDepartHalfList) {
    // ... 处理员工信息 ...
    
    // 自动执行数据清理
    if (CollectionUtil.isNotEmpty(allEmployeeInfos)) {
        cleanupOrphanedUsers(allEmployeeInfos);
    }
}
```

## 数据表结构

### 涉及的主要表
1. **unify_user_new** - 统一用户主表
   - `id` - 用户ID（对应员工工号）
   - `name` - 用户姓名
   - `dept_id` - 关联部门ID
   - `delete_time` - 删除时间
   - `operator_type` - 操作类型（0-新增，1-更新，2-删除）

2. **unify_user_new_extend_info** - 用户扩展信息表
   - `user_id` - 用户ID
   - `user_dept_id` - 用户关联部门ID
   - `jzjg` - 矫正机构ID

## 安全考虑

### 1. 数据安全
- 使用软删除，不会物理删除数据
- 详细的日志记录，便于追踪和回滚
- 异常处理机制，单个用户清理失败不影响整体流程

### 2. 性能考虑
- 批量查询减少数据库访问次数
- 使用 Set 集合进行快速员工工号查找
- 异步推送机制避免阻塞主流程

### 3. 使用建议
- **测试环境验证**：在生产环境使用前，请在测试环境充分验证
- **备份数据**：建议在执行清理前备份相关数据表
- **监控日志**：密切关注清理过程的日志输出
- **分批执行**：对于大量数据，建议分批执行清理操作

## 日志说明

### 日志级别
- **INFO**：关键操作信息（开始清理、完成清理、统计信息）
- **DEBUG**：详细操作信息（单个用户处理详情）
- **WARN**：警告信息（推送失败等非关键错误）
- **ERROR**：错误信息（清理失败、异常情况）

### 关键日志示例
```
INFO  - 开始执行数据同步清理，当前员工信息数量: 1500
INFO  - 有效员工工号数量: 1500
INFO  - unify_user_new 表中用户总数: 1650
INFO  - 发现需要清理的孤立用户数量: 25
INFO  - 开始清理孤立用户: 张三 (EMP001)
INFO  - 成功清理孤立用户: 张三 (EMP001)
INFO  - 孤立用户数据清理完成，成功清理 25 个用户
INFO  - 开始触发组织架构数据推送
INFO  - 组织架构数据推送完成
INFO  - 数据同步清理完成
```

## 故障排除

### 常见问题
1. **清理数量异常**：检查员工信息获取是否正确
2. **推送失败**：检查网络连接和目标系统状态
3. **性能问题**：考虑分批处理或优化查询条件

### 回滚操作
如需回滚清理操作，可以：
1. 将 `delete_time` 字段设置为 NULL
2. 将 `operator_type` 字段重置为之前的值
3. 重新创建被删除的扩展信息记录

## 版本历史

- **v1.0** - 初始版本，实现基本的孤立用户清理功能
- 支持软删除和组织推送
- 集成到现有同步流程中
